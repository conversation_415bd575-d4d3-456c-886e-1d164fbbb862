import { LeadAvatar } from "@components/avatar";
import type { LeadStatusType } from "@components/badge/leadStatusBadge";
import { Input, Select, Textarea } from "@components/common";
import type { FormField, FormValues } from "@components/leadDrawer/interface";
import { useForm } from "@tanstack/react-form";
import { cn } from "@utils/cn";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { leadProfileField } from "./prolfileFields";
import { Button } from "@components/common/button";

export const LeadProfileForm = () => {
  const { t } = useTranslation();

  const today = useMemo(() => new Date().toISOString().split("T")[0], []);

  const noteField: FormField = useMemo(
    () => ({
      className: "w-full resize-none",
      id: "note",
      label: t("addLead.note"),
      placeholder: t("addLead.note"),
      type: "textarea" as const,
    }),
    [t],
  );

  const formFields = useMemo(() => leadProfileField(t), [t]);

  const defaultValues = useMemo(
    () => ({
      contactChannel: "line",
      contactInfo: "1150",
      followUpDate: today,
      followUpStatus: "pending",
      lastFollowUpDate: today,
      leadStatus: "active",
      name: "Aims",
      note: "บ่มีจ้า",
      opportunity: "hot",
      servicesOfInterest: "botox",
      startDate: today,
      totalDayFromStartDate: "1",
      totalDayToNextFollowUp: "1",
    }),
    [today],
  );

  const handleFormSubmit = useCallback(async (value: FormValues) => {
    alert(JSON.stringify(value, null, 2));
  }, []);

  const form = useForm({
    defaultValues,
    onSubmit: async ({ value }: { value: FormValues }) => {
      await handleFormSubmit(value);
    },
  });

  const renderLeadDetails = useCallback(() => {
    return (
      <div className="flex items-center gap-4">
        <form.Field name="leadStatus">
          {(fieldApi) => (
            <LeadAvatar
              image="https://img.daisyui.com/images/profile/demo/<EMAIL>"
              leadStatus={fieldApi.state.value as LeadStatusType}
              onLeadStatusChange={(status) => fieldApi.handleChange(status)}
            />
          )}
        </form.Field>
        <form.Field name="name">
          {(fieldApi) => (
            <Input
              id={fieldApi.name}
              type="text"
              placeholder="Name"
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
              className="flex-1 group-hover:bg-base-200"
              variant="transparent"
              fontSize="xl"
            />
          )}
        </form.Field>
      </div>
    );
  }, [form]);

  const renderField = useCallback(
    (field: FormField) => {
      const validators = field.required
        ? {
            onChange: ({ value }: { value: string }) =>
              !value ? `${field.label} is required` : undefined,
          }
        : {};

      return (
        <form.Field key={field.id} name={field.id as keyof FormValues} validators={validators}>
          {(fieldApi) => {
            const isErrors = fieldApi.state.meta.errors.length > 0;
            return (
              <div className="grid grid-cols-2 gap-1">
                <label htmlFor={fieldApi.name} className="flex gap-1 self-center text-h6">
                  <span className="text-h6">{field.label}</span>
                  {field.required && <span className="text-error text-h6">*</span>}
                </label>

                <div className="relative">
                  {field.type === "input" && (
                    <Input
                      id={fieldApi.name}
                      type={field.inputType || "text"}
                      placeholder={field.placeholder}
                      value={fieldApi.state.value}
                      onChange={(e) => fieldApi.handleChange(e.target.value)}
                      disabled={field.disabled}
                      className={cn(
                        "!bg-transparent hover:!bg-base-200 flex-1",
                        field.variant === "transparent" ? "group-hover:bg-base-200" : "",
                        isErrors ? "outline-1 outline-error" : "",
                      )}
                      variant={field.variant || "default"}
                    />
                  )}

                  {field.type === "date" && (
                    <Input
                      id={fieldApi.name}
                      type="date"
                      value={fieldApi.state.value}
                      onChange={(e) => fieldApi.handleChange(e.target.value)}
                      disabled={field.disabled}
                      className={cn(
                        "!bg-transparent hover:!bg-base-200 flex-1",
                        isErrors ? "outline-1 outline-error" : "",
                      )}
                      variant="transparent"
                    />
                  )}

                  {field.type === "select" && (
                    <Select
                      id={fieldApi.name}
                      options={field.options}
                      size="sm"
                      variant="popup"
                      value={fieldApi.state.value}
                      onChange={(value) => fieldApi.handleChange(value)}
                      className={cn("flex-1", isErrors ? "outline-1 outline-error" : "")}
                      placeholder={field.placeholder}
                    />
                  )}

                  {isErrors && (
                    <div className="-bottom-5 absolute left-1 text-error text-xs">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              </div>
            );
          }}
        </form.Field>
      );
    },
    [form],
  );

  const renderNoteField = useCallback(
    (field: FormField) => {
      return (
        <form.Field key={field.id} name={field.id as keyof FormValues}>
          {(fieldApi) => (
            <div className="flex min-h-0 flex-1 flex-col gap-2">
              <label htmlFor={fieldApi.name} className="text-h6">
                {field.label}
              </label>
              <Textarea
                className={field.className}
                id={fieldApi.name}
                placeholder={field.placeholder}
                value={fieldApi.state.value}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
              />
              {fieldApi.state.meta.errors.length > 0 && (
                <div className="text-error text-sm">{fieldApi.state.meta.errors[0]}</div>
              )}
            </div>
          )}
        </form.Field>
      );
    },
    [form],
  );

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      e.stopPropagation();
      form.handleSubmit();
    },
    [form],
  );

  return (
    <form className="flex h-full flex-col gap-2 overflow-hidden p-1" onSubmit={handleSubmit}>
      {renderLeadDetails()}
      {formFields.map((field) => renderField(field))}
      {renderNoteField(noteField)}
      <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
        {([canSubmit, isSubmitting]) => (
          <Button className="w-28" type="submit" disabled={!canSubmit || isSubmitting}>
            {isSubmitting ? t("common.loading") : t("common.save")}
          </Button>
        )}
      </form.Subscribe>
    </form>
  );
};
